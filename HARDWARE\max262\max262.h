//-----------------------------------------------------------------
// MAX262可编程滤波器驱动头文件 (STM32F407版本)
// 文件名: max262.h
// 作者: AI Assistant
// 编写时间: 2025-01-01
// 修改记录: 适配STM32F407，避免与现有工程引脚冲突
//-----------------------------------------------------------------

#ifndef _MAX262_H
#define _MAX262_H

#include "stm32f4xx.h"
#include "sys.h"
#include "delay.h"

//-----------------------------------------------------------------
// MAX262引脚定义 (避免与现有工程冲突)
// 使用GPIOB和GPIOC的可用引脚
//-----------------------------------------------------------------

// 数据线 D0, D1 (2位数据)
#define MAX262_D0_GPIO_PORT         GPIOB
#define MAX262_D0_GPIO_PIN          GPIO_Pin_0      // PB0
#define MAX262_D0_L                 GPIO_ResetBits(GPIOB, GPIO_Pin_0)
#define MAX262_D0_H                 GPIO_SetBits(GPIOB, GPIO_Pin_0)

#define MAX262_D1_GPIO_PORT         GPIOB
#define MAX262_D1_GPIO_PIN          GPIO_Pin_1      // PB1
#define MAX262_D1_L                 GPIO_ResetBits(GPIOB, GPIO_Pin_1)
#define MAX262_D1_H                 GPIO_SetBits(GPIOB, GPIO_Pin_1)

// 地址线 A0-A3 (4位地址)
#define MAX262_A0_GPIO_PORT         GPIOB
#define MAX262_A0_GPIO_PIN          GPIO_Pin_2      // PB2
#define MAX262_A0_L                 GPIO_ResetBits(GPIOB, GPIO_Pin_2)
#define MAX262_A0_H                 GPIO_SetBits(GPIOB, GPIO_Pin_2)

#define MAX262_A1_GPIO_PORT         GPIOB
#define MAX262_A1_GPIO_PIN          GPIO_Pin_3      // PB3
#define MAX262_A1_L                 GPIO_ResetBits(GPIOB, GPIO_Pin_3)
#define MAX262_A1_H                 GPIO_SetBits(GPIOB, GPIO_Pin_3)

#define MAX262_A2_GPIO_PORT         GPIOB
#define MAX262_A2_GPIO_PIN          GPIO_Pin_4      // PB4
#define MAX262_A2_L                 GPIO_ResetBits(GPIOB, GPIO_Pin_4)
#define MAX262_A2_H                 GPIO_SetBits(GPIOB, GPIO_Pin_4)

#define MAX262_A3_GPIO_PORT         GPIOB
#define MAX262_A3_GPIO_PIN          GPIO_Pin_5      // PB5
#define MAX262_A3_L                 GPIO_ResetBits(GPIOB, GPIO_Pin_5)
#define MAX262_A3_H                 GPIO_SetBits(GPIOB, GPIO_Pin_5)

// 控制线
#define MAX262_WR_GPIO_PORT         GPIOC
#define MAX262_WR_GPIO_PIN          GPIO_Pin_0      // PC0
#define MAX262_WR_L                 GPIO_ResetBits(GPIOC, GPIO_Pin_0)
#define MAX262_WR_H                 GPIO_SetBits(GPIOC, GPIO_Pin_0)

#define MAX262_LE_GPIO_PORT         GPIOC
#define MAX262_LE_GPIO_PIN          GPIO_Pin_2      // PC2
#define MAX262_LE_L                 GPIO_ResetBits(GPIOC, GPIO_Pin_2)
#define MAX262_LE_H                 GPIO_SetBits(GPIOC, GPIO_Pin_2)

//-----------------------------------------------------------------
// MAX262寄存器地址定义
//-----------------------------------------------------------------
#define MAX262_MODE_ADDR            0x00    // 模式寄存器地址
#define MAX262_F0_ADDR_BASE         0x01    // f0寄存器基地址 (0x01-0x03)
#define MAX262_Q_ADDR_BASE          0x04    // Q寄存器基地址 (0x04-0x07)

// 滤波器1地址偏移
#define MAX262_FILTER1_OFFSET       0x00
// 滤波器2地址偏移  
#define MAX262_FILTER2_OFFSET       0x08

//-----------------------------------------------------------------
// MAX262模式定义
//-----------------------------------------------------------------
#define MAX262_MODE_LOWPASS         0x00    // 低通滤波器
#define MAX262_MODE_HIGHPASS        0x01    // 高通滤波器
#define MAX262_MODE_BANDPASS        0x02    // 带通滤波器
#define MAX262_MODE_BANDSTOP        0x03    // 带阻滤波器

//-----------------------------------------------------------------
// MAX262时钟频率定义 (可根据实际硬件调整)
//-----------------------------------------------------------------
#define MAX262_CLOCK_FREQ_1MHZ      1000000.0f
#define MAX262_CLOCK_FREQ_2MHZ      2000000.0f
#define MAX262_CLOCK_FREQ_4MHZ      4000000.0f
#define MAX262_CLOCK_FREQ_8MHZ      8000000.0f

// 默认时钟频率
#define MAX262_DEFAULT_CLOCK        MAX262_CLOCK_FREQ_1MHZ

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void);
void MAX262_Init(void);
void MAX262_WriteRegister(uint8_t address, uint8_t data);
uint8_t MAX262_CalculateFreqCode(float frequency, float clock_freq);
uint8_t MAX262_CalculateQCode(float q_factor);
void MAX262_ConfigFilter1(uint8_t mode, float frequency, float q_factor, float clock_freq);
void MAX262_ConfigFilter2(uint8_t mode, float frequency, float q_factor, float clock_freq);
void MAX262_SetMode(uint8_t filter, uint8_t mode);
void MAX262_SetFrequency(uint8_t filter, float frequency, float clock_freq);
void MAX262_SetQFactor(uint8_t filter, float q_factor);

// 便捷配置函数
void MAX262_ConfigLowPass(uint8_t filter, float cutoff_freq, float q_factor);
void MAX262_ConfigHighPass(uint8_t filter, float cutoff_freq, float q_factor);
void MAX262_ConfigBandPass(uint8_t filter, float center_freq, float q_factor);
void MAX262_ConfigBandStop(uint8_t filter, float center_freq, float q_factor);

// 测试函数
void MAX262_Test(void);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
