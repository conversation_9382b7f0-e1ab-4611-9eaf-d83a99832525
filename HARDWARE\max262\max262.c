//-----------------------------------------------------------------
// MAX262可编程滤波器驱动实现 (STM32F407版本)
// 文件名: max262.c
// 作者: AI Assistant
// 编写时间: 2025-01-01
// 修改记录: 适配STM32F407，基于STM32F103例程改写
//-----------------------------------------------------------------

#include "max262.h"
#include <math.h>
#include <stdio.h>

// 全局变量
static float g_max262_clock_freq = MAX262_DEFAULT_CLOCK;

//-----------------------------------------------------------------
// void MAX262_GPIO_Init(void)
//-----------------------------------------------------------------
// 功能描述: 初始化MAX262控制引脚
// 输入参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 配置为推挽输出，高速模式
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOB和GPIOC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB | RCC_AHB1Periph_GPIOC, ENABLE);
    
    // 配置GPIOB引脚 (D0, D1, A0-A3)
    GPIO_InitStructure.GPIO_Pin = MAX262_D0_GPIO_PIN | MAX262_D1_GPIO_PIN | 
                                  MAX262_A0_GPIO_PIN | MAX262_A1_GPIO_PIN |
                                  MAX262_A2_GPIO_PIN | MAX262_A3_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    
    // 配置GPIOC引脚 (WR, LE)
    GPIO_InitStructure.GPIO_Pin = MAX262_WR_GPIO_PIN | MAX262_LE_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    
    // 设置初始状态
    MAX262_LE_H;    // 锁存使能高电平
    MAX262_WR_H;    // 写使能高电平
    
    // 清零所有数据和地址线
    MAX262_D0_L;
    MAX262_D1_L;
    MAX262_A0_L;
    MAX262_A1_L;
    MAX262_A2_L;
    MAX262_A3_L;
}

//-----------------------------------------------------------------
// void MAX262_Init(void)
//-----------------------------------------------------------------
// 功能描述: 初始化MAX262芯片
// 输入参数: 无
// 返 回 值: 无
// 全局变量: g_max262_clock_freq
// 注意事项: 先初始化GPIO，然后复位芯片
//-----------------------------------------------------------------
void MAX262_Init(void)
{
    MAX262_GPIO_Init();
    
    // 延时等待芯片稳定
    delay_ms(10);
    
    // 设置默认时钟频率
    g_max262_clock_freq = MAX262_DEFAULT_CLOCK;
    
    // 初始化两个滤波器为低通模式，默认参数
    MAX262_ConfigLowPass(1, 10000.0f, 0.707f);  // 滤波器1: 10kHz低通，Q=0.707
    MAX262_ConfigLowPass(2, 10000.0f, 0.707f);  // 滤波器2: 10kHz低通，Q=0.707
}

//-----------------------------------------------------------------
// void MAX262_WriteRegister(uint8_t address, uint8_t data)
//-----------------------------------------------------------------
// 功能描述: 向MAX262写入寄存器数据
// 输入参数: address - 寄存器地址 (4位)
//          data - 数据 (2位)
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 严格按照MAX262时序要求
//-----------------------------------------------------------------
void MAX262_WriteRegister(uint8_t address, uint8_t data)
{
    // 1. 设置地址
    if (address & 0x01) MAX262_A0_H; else MAX262_A0_L;
    if (address & 0x02) MAX262_A1_H; else MAX262_A1_L;
    if (address & 0x04) MAX262_A2_H; else MAX262_A2_L;
    if (address & 0x08) MAX262_A3_H; else MAX262_A3_L;
    
    delay_us(1);  // 地址建立时间
    
    // 2. WR下降沿开始写入
    MAX262_WR_L;
    delay_us(1);
    
    // 3. 设置数据
    if (data & 0x01) MAX262_D0_H; else MAX262_D0_L;
    if (data & 0x02) MAX262_D1_H; else MAX262_D1_L;
    
    delay_us(1);  // 数据建立时间
    
    // 4. WR上升沿锁存数据
    MAX262_WR_H;
    delay_us(1);
}

//-----------------------------------------------------------------
// uint8_t MAX262_CalculateFreqCode(float frequency, float clock_freq)
//-----------------------------------------------------------------
// 功能描述: 计算频率控制码
// 输入参数: frequency - 目标频率 (Hz)
//          clock_freq - 时钟频率 (Hz)
// 返 回 值: 频率控制码 (8位)
// 全局变量: 无
// 注意事项: 基于MAX262数据手册公式
//-----------------------------------------------------------------
uint8_t MAX262_CalculateFreqCode(float frequency, float clock_freq)
{
    // MAX262频率关系: f0 = fCLK / (256 * N)
    // 其中 N = freq_code + 1
    float N = clock_freq / (256.0f * frequency);
    
    // 限制N的范围 (1 到 256)
    if (N < 1.0f) N = 1.0f;
    if (N > 256.0f) N = 256.0f;
    
    uint8_t freq_code = (uint8_t)(N - 1.0f + 0.5f);  // 四舍五入
    
    return freq_code;
}

//-----------------------------------------------------------------
// uint8_t MAX262_CalculateQCode(float q_factor)
//-----------------------------------------------------------------
// 功能描述: 计算Q值控制码
// 输入参数: q_factor - Q值
// 返 回 值: Q值控制码 (8位)
// 全局变量: 无
// 注意事项: 基于MAX262数据手册的Q值映射关系
//-----------------------------------------------------------------
uint8_t MAX262_CalculateQCode(float q_factor)
{
    uint8_t q_code;
    
    // 限制Q值范围
    if (q_factor < 0.5f) q_factor = 0.5f;
    if (q_factor > 256.0f) q_factor = 256.0f;
    
    // MAX262 Q值编码 (简化线性映射)
    // 实际关系较复杂，这里使用近似公式
    if (q_factor <= 1.0f) {
        q_code = (uint8_t)(q_factor * 64.0f);
    } else if (q_factor <= 4.0f) {
        q_code = (uint8_t)(64.0f + (q_factor - 1.0f) * 48.0f);
    } else {
        q_code = (uint8_t)(208.0f + (q_factor - 4.0f) * 12.0f);
    }
    
    if (q_code > 255) q_code = 255;
    
    return q_code;
}

//-----------------------------------------------------------------
// void MAX262_ConfigFilter1(uint8_t mode, float frequency, float q_factor, float clock_freq)
//-----------------------------------------------------------------
// 功能描述: 配置滤波器1
// 输入参数: mode - 滤波器模式
//          frequency - 频率 (Hz)
//          q_factor - Q值
//          clock_freq - 时钟频率 (Hz)
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 按照MAX262寄存器映射配置
//-----------------------------------------------------------------
void MAX262_ConfigFilter1(uint8_t mode, float frequency, float q_factor, float clock_freq)
{
    uint8_t freq_code = MAX262_CalculateFreqCode(frequency, clock_freq);
    uint8_t q_code = MAX262_CalculateQCode(q_factor);
    
    // 1. 写入模式寄存器
    MAX262_WriteRegister(MAX262_MODE_ADDR + MAX262_FILTER1_OFFSET, mode & 0x03);
    
    // 2. 写入频率寄存器 (3个字节，每次2位)
    for (int i = 0; i < 3; i++) {
        uint8_t addr = MAX262_F0_ADDR_BASE + i + MAX262_FILTER1_OFFSET;
        uint8_t data = (freq_code >> (2 * i)) & 0x03;
        MAX262_WriteRegister(addr, data);
    }
    
    // 3. 写入Q值寄存器 (4个字节，每次2位)
    for (int i = 0; i < 4; i++) {
        uint8_t addr = MAX262_Q_ADDR_BASE + i + MAX262_FILTER1_OFFSET;
        uint8_t data = (q_code >> (2 * i)) & 0x03;
        MAX262_WriteRegister(addr, data);
    }
}

//-----------------------------------------------------------------
// void MAX262_ConfigFilter2(uint8_t mode, float frequency, float q_factor, float clock_freq)
//-----------------------------------------------------------------
// 功能描述: 配置滤波器2
// 输入参数: mode - 滤波器模式
//          frequency - 频率 (Hz)
//          q_factor - Q值
//          clock_freq - 时钟频率 (Hz)
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 与滤波器1类似，但地址偏移+8
//-----------------------------------------------------------------
void MAX262_ConfigFilter2(uint8_t mode, float frequency, float q_factor, float clock_freq)
{
    uint8_t freq_code = MAX262_CalculateFreqCode(frequency, clock_freq);
    uint8_t q_code = MAX262_CalculateQCode(q_factor);

    // 1. 写入模式寄存器
    MAX262_WriteRegister(MAX262_MODE_ADDR + MAX262_FILTER2_OFFSET, mode & 0x03);

    // 2. 写入频率寄存器 (3个字节，每次2位)
    for (int i = 0; i < 3; i++) {
        uint8_t addr = MAX262_F0_ADDR_BASE + i + MAX262_FILTER2_OFFSET;
        uint8_t data = (freq_code >> (2 * i)) & 0x03;
        MAX262_WriteRegister(addr, data);
    }

    // 3. 写入Q值寄存器 (4个字节，每次2位)
    for (int i = 0; i < 4; i++) {
        uint8_t addr = MAX262_Q_ADDR_BASE + i + MAX262_FILTER2_OFFSET;
        uint8_t data = (q_code >> (2 * i)) & 0x03;
        MAX262_WriteRegister(addr, data);
    }
}

//-----------------------------------------------------------------
// 便捷配置函数
//-----------------------------------------------------------------

void MAX262_ConfigLowPass(uint8_t filter, float cutoff_freq, float q_factor)
{
    if (filter == 1) {
        MAX262_ConfigFilter1(MAX262_MODE_LOWPASS, cutoff_freq, q_factor, g_max262_clock_freq);
    } else if (filter == 2) {
        MAX262_ConfigFilter2(MAX262_MODE_LOWPASS, cutoff_freq, q_factor, g_max262_clock_freq);
    }
}

void MAX262_ConfigHighPass(uint8_t filter, float cutoff_freq, float q_factor)
{
    if (filter == 1) {
        MAX262_ConfigFilter1(MAX262_MODE_HIGHPASS, cutoff_freq, q_factor, g_max262_clock_freq);
    } else if (filter == 2) {
        MAX262_ConfigFilter2(MAX262_MODE_HIGHPASS, cutoff_freq, q_factor, g_max262_clock_freq);
    }
}

void MAX262_ConfigBandPass(uint8_t filter, float center_freq, float q_factor)
{
    if (filter == 1) {
        MAX262_ConfigFilter1(MAX262_MODE_BANDPASS, center_freq, q_factor, g_max262_clock_freq);
    } else if (filter == 2) {
        MAX262_ConfigFilter2(MAX262_MODE_BANDPASS, center_freq, q_factor, g_max262_clock_freq);
    }
}

void MAX262_ConfigBandStop(uint8_t filter, float center_freq, float q_factor)
{
    if (filter == 1) {
        MAX262_ConfigFilter1(MAX262_MODE_BANDSTOP, center_freq, q_factor, g_max262_clock_freq);
    } else if (filter == 2) {
        MAX262_ConfigFilter2(MAX262_MODE_BANDSTOP, center_freq, q_factor, g_max262_clock_freq);
    }
}

//-----------------------------------------------------------------
// void MAX262_Test(void)
//-----------------------------------------------------------------
// 功能描述: MAX262测试函数
// 输入参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 用于验证驱动程序功能
//-----------------------------------------------------------------
void MAX262_Test(void)
{
    printf("\r\n=== MAX262 Driver Test ===\r\n");

    // 测试滤波器1配置
    printf("Configuring Filter1 as 10kHz Low-pass, Q=0.707\r\n");
    MAX262_ConfigLowPass(1, 10000.0f, 0.707f);
    delay_ms(100);

    // 测试滤波器2配置
    printf("Configuring Filter2 as 5kHz High-pass, Q=1.0\r\n");
    MAX262_ConfigHighPass(2, 5000.0f, 1.0f);
    delay_ms(100);

    printf("MAX262 Test Completed!\r\n");
    printf("=== End of Test ===\r\n\r\n");
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
