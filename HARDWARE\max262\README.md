# MAX262可编程滤波器驱动程序

## 📋 概述

本驱动程序为STM32F407微控制器提供MAX262可编程滤波器的完整控制接口。MAX262是一个双通道、开关电容滤波器，支持低通、高通、带通和带阻四种滤波模式。

## 🔌 硬件连接

### 引脚分配
```
MAX262引脚    STM32F407引脚    功能描述
----------------------------------------
D0           PB0              数据线0
D1           PB1              数据线1
A0           PB2              地址线0
A1           PB3              地址线1
A2           PB4              地址线2
A3           PB5              地址线3
WR           PC0              写使能
LE           PC2              锁存使能
```

### 注意事项
- 所有引脚均避开了现有工程使用的引脚
- 引脚配置为推挽输出，高速模式
- 需要为MAX262提供适当的时钟信号

## 🚀 使用方法

### 1. 初始化
```c
#include "max262.h"

int main(void)
{
    // 系统初始化...
    
    MAX262_Init();  // 初始化MAX262
    
    // 其他代码...
}
```

### 2. 基本配置
```c
// 配置滤波器1为10kHz低通，Q=0.707
MAX262_ConfigLowPass(1, 10000.0f, 0.707f);

// 配置滤波器2为5kHz高通，Q=1.0
MAX262_ConfigHighPass(2, 5000.0f, 1.0f);

// 配置带通滤波器
MAX262_ConfigBandPass(1, 15000.0f, 2.0f);

// 配置带阻滤波器
MAX262_ConfigBandStop(2, 8000.0f, 1.5f);
```

### 3. 高级配置
```c
// 使用自定义时钟频率配置
MAX262_ConfigFilter1(MAX262_MODE_LOWPASS, 12000.0f, 0.8f, 2000000.0f);

// 单独设置参数
MAX262_SetFrequency(1, 15000.0f, 1000000.0f);
MAX262_SetQFactor(1, 1.2f);
MAX262_SetMode(1, MAX262_MODE_BANDPASS);
```

## 📊 自动配置功能

驱动程序集成了自动配置功能，可以根据扫频测试结果自动配置MAX262：

```c
// 在扫频测试完成后，程序会自动：
// 1. 分析测得的滤波器参数
// 2. 计算MAX262配置参数
// 3. 自动配置MAX262硬件
// 4. 输出配置结果
```

## 🔧 API参考

### 初始化函数
- `MAX262_Init()` - 初始化MAX262芯片

### 配置函数
- `MAX262_ConfigLowPass(filter, freq, q)` - 配置低通滤波器
- `MAX262_ConfigHighPass(filter, freq, q)` - 配置高通滤波器
- `MAX262_ConfigBandPass(filter, freq, q)` - 配置带通滤波器
- `MAX262_ConfigBandStop(filter, freq, q)` - 配置带阻滤波器

### 底层函数
- `MAX262_WriteRegister(addr, data)` - 写寄存器
- `MAX262_CalculateFreqCode(freq, clock)` - 计算频率码
- `MAX262_CalculateQCode(q)` - 计算Q值码

### 测试函数
- `MAX262_Test()` - 驱动程序测试

## 📈 参数范围

### 频率范围
- 最小频率：fCLK/65536
- 最大频率：fCLK/256
- 对于1MHz时钟：15.26Hz - 3906.25Hz
- 对于8MHz时钟：122Hz - 31.25kHz

### Q值范围
- 最小Q值：0.5
- 最大Q值：256
- 推荐范围：0.5 - 10

### 时钟频率
- 支持1MHz、2MHz、4MHz、8MHz
- 默认使用1MHz

## 🔍 故障排除

### 常见问题
1. **配置无效果**
   - 检查硬件连接
   - 确认时钟信号正常
   - 验证引脚配置

2. **频率不准确**
   - 检查时钟频率设置
   - 验证计算公式
   - 考虑器件容差

3. **Q值异常**
   - 确认Q值在有效范围内
   - 检查模拟电路设计
   - 验证负载阻抗

## 📝 版本历史

- v1.0 (2025-01-01)
  - 初始版本
  - 支持STM32F407
  - 集成自动配置功能
  - 完整的API接口

## 📞 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 引脚配置是否冲突
3. 时钟信号是否稳定
4. 参数设置是否在有效范围内
