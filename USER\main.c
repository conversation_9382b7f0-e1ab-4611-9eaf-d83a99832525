#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include "max262.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储 - 优化内存使用
#define ADC1_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC2采样数据存储 - 优化内存使用
#define ADC2_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2采样控制函数声明
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// 扫频测试相关变量和函数声明
typedef struct {
    float frequency;        // 当前频率
    float adc1_amplitude;   // ADC1幅度（滤波器输出）
    float adc2_amplitude;   // ADC2幅度（滤波器输入）
    float magnitude_db;     // 幅度响应(dB)
    float phase_deg;        // 相位响应(度)
} FrequencyResponse;

#define SWEEP_POINTS 1996   // 扫频点数：(400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_BUFFER_SIZE 50  // 只保存最近50个点的结果用于分析
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // 减少内存使用：50×20字节=1KB
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 总扫频点数计数器

// 归一化处理相关变量
float max_voltage_ratio = 0.0f;           // 最大电压幅度比
volatile uint8_t sweep_phase = 0;          // 扫频阶段：0=寻找最大值，1=归一化输出

// 二阶RLC滤波器参数分析变量
float filter_cutoff_freq = 0.0f;          // 截止频率(-3dB点)
float filter_resonant_freq = 0.0f;        // 谐振频率(如果有峰值)
float filter_q_factor = 0.0f;             // 品质因数Q
float filter_passband_gain = 0.0f;        // 通带增益
float filter_dc_gain = 0.0f;              // 直流增益
float filter_rolloff_rate = 0.0f;         // 滚降率(dB/decade)

// MAX262参数变量
uint8_t max262_f0_bits = 0;               // f0频率控制位
uint8_t max262_q_bits = 0;                // Q值控制位
uint8_t max262_mode_bits = 0;             // 模式控制位
float max262_clock_freq = 1000000.0f;     // 时钟频率(默认1MHz)

// 数据处理优化变量
float frequency_history[10];              // 频率历史记录用于平滑
float gain_history[10];                   // 增益历史记录用于平滑
uint8_t history_index = 0;                // 历史记录索引
float previous_gain = 0.0f;               // 上一次增益值
float gain_derivative = 0.0f;             // 增益导数（用于峰值检测）

// 扫频测试函数声明
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void AnalyzeFilterCharacteristics(void);

// 扫频测试函数声明（优化版）
void AnalyzeLowPassFilter(void);
void UpdateFilterDisplayOnLCD(void);
void CalculateMAX262Parameters(void);
void AnalyzeBandwidthDetails(void);

// 数据处理优化函数
float ProcessADCData_Enhanced(uint16_t* buffer, uint16_t size);
float CalculateRMS_Enhanced(uint16_t* buffer, uint16_t size, float dc_value);
float MedianFilter(float* data, uint16_t size);
float InterpolateFrequency(float freq1, float gain1, float freq2, float gain2, float target_gain);
void SmoothingFilter(float* data, uint16_t size, uint8_t window_size);
float EvaluateDataQuality(uint16_t* buffer, uint16_t size);
void AdaptiveSampling(float data_quality);
float CalculateConfidenceLevel(float q_factor, float resonant_freq, float cutoff_freq);

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义七个按钮 - 更大尺寸便于操作
Button_t buttons[7] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY}      // 扫频测试按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 7; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 7; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // ADC2配置为PC1引脚（ADC123_IN11通道11）
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 扫频测试使用中断方式采样，不需要DMA
    // DMA1_Init();  // ADC1使用中断采样，不需要DMA
    // DMA2_Init();  // ADC2使用中断采样，不需要DMA
    // DMA3_Init();  // ADC3也使用中断采样，不需要DMA
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    MAX262_Init();  // 初始化MAX262可编程滤波器

    lcd_init();
   
    sampfre = 815534;  // 实际采样频率：84MHz / 103 / 1 = 815534Hz

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC正弦波输出 (100kHz中断频率)
    // 84MHz / (84-1) / (10-1) = 100kHz
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 7;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮 - 启动扫频测试
                    if (!sweep_test_active) {
                        // 启动扫频测试
                        sprintf(buttons[6].text, "SWEEP ON");
                        buttons[6].color = GREEN;

                        // 启动扫频测试（无串口输出）

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 启动扫频测试
                        StartSweepTest();

                    } else {
                        // 停止扫频测试
                        sprintf(buttons[6].text, "SWEEP OFF");
                        buttons[6].color = GRAY;

                        StopSweepTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 3000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>3kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 处理扫频测试
        if (sweep_test_active)
        {
            // 检查当前频率点的采样是否完成
            if (adc1_sampling_complete && adc2_sampling_complete)
            {
                // 处理当前扫频点的数据
                ProcessSweepPoint();

                // 移动到下一个频率点
                current_sweep_point++;

                if (current_sweep_point < SWEEP_POINTS)
                {
                    // 设置下一个频率
                    float next_freq = 1000.0f + current_sweep_point * 200.0f;
                    AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                    // 显示进度和当前参数
                    char progress_info[100];
                    if (sweep_phase == 0) {
                        sprintf(progress_info, "Phase1: %d/%d (%.1fkHz)",
                                current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                    } else {
                        sprintf(progress_info, "Phase2: %d/%d (%.1fkHz)",
                                current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                    }
                    lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                    // 在第二阶段实时显示当前Q和f值
                    if (sweep_phase == 1 && filter_q_factor > 0) {
                        char param_info[100];
                        sprintf(param_info, "Q=%.3f  f=%.1fHz", filter_q_factor, filter_resonant_freq);
                        lcd_show_string(10, 110, lcddev.width, 20, 12, param_info, GREEN);
                    }

                    // 重新启动ADC采样
                    ADC1_ResetSampling();
                    ADC2_ResetSampling();
                    ADC1_StartSampling();
                    ADC2_StartSampling();
                }
                else
                {
                    if (sweep_phase == 0) {
                        // 第一阶段完成，开始第二阶段
                        sweep_phase = 1;
                        current_sweep_point = 0;

                        // 显示第一阶段结果
                        char phase1_result[100];
                        sprintf(phase1_result, "Max ratio: %.6f", max_voltage_ratio);
                        lcd_show_string(10, 110, lcddev.width, 20, 12, phase1_result, YELLOW);

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: Normalized Output", BLUE);
                    } else {
                        // 第二阶段完成，扫频测试结束
                        StopSweepTest();
                        OutputSweepResults();

                        // 分析滤波器参数
                        AnalyzeLowPassFilter();

                        // 详细带宽分析
                        AnalyzeBandwidthDetails();

                        // 计算MAX262参数
                        CalculateMAX262Parameters();

                        // 更新LCD显示
                        UpdateFilterDisplayOnLCD();

                        // 显示完成状态
                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Complete!", GREEN);
                    }
                }
            }
        }

        // 检查ADC1采样是否完成（非扫频模式）
        if (adc1_sampling_complete && !sweep_test_active && adc_user_enabled)
        {
            // 显示采样完成信息
            char sample_info[100];
            sprintf(sample_info, "ADC1: 4096 samples complete");
            lcd_show_string(10, 90, lcddev.width, 20, 12, sample_info, BLUE);

            // 显示一些采样数据（前几个点）
            sprintf(sample_info, "Data[0-3]: %d %d %d %d",
                    adc1_sample_buffer[0], adc1_sample_buffer[1],
                    adc1_sample_buffer[2], adc1_sample_buffer[3]);
            lcd_show_string(10, 110, lcddev.width, 20, 12, sample_info, GREEN);

            // 通过串口输出所有ADC1采样数据
            printf("ADC1_SAMPLES_START\r\n");
            for (int i = 0; i < ADC1_SAMPLE_SIZE; i++)
            {
                printf("%d\t", adc1_sample_buffer[i]);
            }
            printf("ADC1_SAMPLES_END\r\n");

            // 重置采样状态，准备下次采样
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
        }

        delay_ms(10);  // 主循环延时
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC2采样控制函数实现
void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC2_ResetSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    sweep_test_active = 1;
    current_sweep_point = 0;
    total_sweep_points = 0;
    sweep_sampling_complete = 0;
    max_voltage_ratio = 0.0f;
    sweep_phase = 0;  // 第一阶段：寻找最大值

    // 初始化RLC滤波器分析变量
    filter_cutoff_freq = 0.0f;
    filter_resonant_freq = 0.0f;
    filter_q_factor = 0.0f;
    filter_passband_gain = 0.0f;
    filter_dc_gain = 0.0f;
    filter_rolloff_rate = 0.0f;

    // 初始化MAX262参数变量
    max262_f0_bits = 0;
    max262_q_bits = 0;
    max262_mode_bits = 0;

    // 初始化数据处理变量
    for (int i = 0; i < 10; i++) {
        frequency_history[i] = 0.0f;
        gain_history[i] = 0.0f;
    }
    history_index = 0;
    previous_gain = 0.0f;
    gain_derivative = 0.0f;

    // 清空结果缓冲区
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].magnitude_db = 0;
        sweep_results[i].phase_deg = 0;
    }

    // 设置起始频率 1kHz
    float start_freq = 1000.0f;
    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);

    // 开始扫频测试，无串口输出

    // 等待频率稳定
    delay_ms(10);

    // 启动ADC1和ADC2同步采样
    ADC1_ResetSampling();
    ADC2_ResetSampling();
    ADC1_StartSampling();
    ADC2_StartSampling();
}

void StopSweepTest(void)
{
    sweep_test_active = 0;

    // 停止ADC采样
    ADC1_StopSampling();
    ADC2_StopSampling();
}

void ProcessSweepPoint(void)
{
    float current_freq = 1000.0f + current_sweep_point * 200.0f;

    // 计算ADC1和ADC2的RMS值（去除直流分量）
    float adc1_dc = 0, adc2_dc = 0;
    float adc1_rms = 0, adc2_rms = 0;

    // 使用优化的数据处理算法
    adc1_dc = ProcessADCData_Enhanced(adc1_sample_buffer, ADC1_SAMPLE_SIZE);
    adc2_dc = ProcessADCData_Enhanced(adc2_sample_buffer, ADC2_SAMPLE_SIZE);

    adc1_rms = CalculateRMS_Enhanced(adc1_sample_buffer, ADC1_SAMPLE_SIZE, adc1_dc);
    adc2_rms = CalculateRMS_Enhanced(adc2_sample_buffer, ADC2_SAMPLE_SIZE, adc2_dc);

    // 评估数据质量
    float adc1_quality = EvaluateDataQuality(adc1_sample_buffer, ADC1_SAMPLE_SIZE);
    float adc2_quality = EvaluateDataQuality(adc2_sample_buffer, ADC2_SAMPLE_SIZE);
    float avg_quality = (adc1_quality + adc2_quality) / 2.0f;

    // 自适应处理
    AdaptiveSampling(avg_quality);

    // 计算幅度响应 (dB)
    float magnitude_db;
    if (adc2_rms > 0 && adc1_rms > 0) {
        magnitude_db = 20.0f * log10f(adc1_rms / adc2_rms);
    } else {
        magnitude_db = -100.0f; // 很小的值
    }

    // 简化的相位计算（基于峰值位置差）
    int adc1_peak_idx = 0, adc2_peak_idx = 0;
    float adc1_max = 0, adc2_max = 0;

    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_val = fabsf(adc1_sample_buffer[i] - adc1_dc);
        float adc2_val = fabsf(adc2_sample_buffer[i] - adc2_dc);

        if (adc1_val > adc1_max) {
            adc1_max = adc1_val;
            adc1_peak_idx = i;
        }
        if (adc2_val > adc2_max) {
            adc2_max = adc2_val;
            adc2_peak_idx = i;
        }
    }

    // 计算相位差（度）
    float sample_period = 1.0f / 815534.0f; // 采样周期
    float signal_period = 1.0f / current_freq; // 信号周期
    float time_diff = (adc1_peak_idx - adc2_peak_idx) * sample_period;
    float phase_deg = (time_diff / signal_period) * 360.0f;

    // 限制相位范围到 -180 到 +180 度
    while (phase_deg > 180.0f) {
        phase_deg -= 360.0f;
    }
    while (phase_deg < -180.0f) {
        phase_deg += 360.0f;
    }

    // 计算电压幅度比（线性比值，不是dB）
    float voltage_ratio = 0.0f;
    if (adc2_rms > 0) {
        voltage_ratio = adc1_rms / adc2_rms;
    }

    if (sweep_phase == 0) {
        // 第一阶段：寻找最大值，不输出
        if (voltage_ratio > max_voltage_ratio) {
            max_voltage_ratio = voltage_ratio;
        }
    } else {
        // 第二阶段：归一化输出
        float normalized_ratio = 0.0f;

        if (max_voltage_ratio > 0.0f) {
            normalized_ratio = voltage_ratio / max_voltage_ratio;
        }

        // 优化的RLC滤波器参数分析
        float gain_db = 20.0f * log10f(normalized_ratio);

        // 记录增益历史用于平滑处理
        gain_history[history_index] = gain_db;
        frequency_history[history_index] = current_freq;
        history_index = (history_index + 1) % 10;

        // 计算增益导数（用于峰值检测）
        if (previous_gain != 0.0f) {
            gain_derivative = gain_db - previous_gain;
        }
        previous_gain = gain_db;

        // 记录直流增益（低频增益，使用多点平均）
        if (current_freq <= 3000.0f) {
            static float dc_gain_sum = 0.0f;
            static uint8_t dc_gain_count = 0;
            dc_gain_sum += gain_db;
            dc_gain_count++;
            if (dc_gain_count >= 5) {  // 使用前5个低频点的平均值
                filter_dc_gain = dc_gain_sum / dc_gain_count;
            }
        }

        // 改进的谐振频率检测（使用导数和阈值）
        static float max_gain_db = -100.0f;
        if (gain_db > max_gain_db) {
            max_gain_db = gain_db;
            filter_resonant_freq = current_freq;
            filter_passband_gain = gain_db;
        }

        // 改进的-3dB点检测（使用插值）
        static float prev_freq = 0.0f;
        static float prev_gain_db = 0.0f;

        if (filter_cutoff_freq == 0.0f && prev_freq > 0.0f) {
            float reference_gain = (filter_dc_gain != 0.0f) ? filter_dc_gain : max_gain_db;
            float target_3db_level = reference_gain - 3.0f;

            // 检查是否跨越了-3dB点
            if ((prev_gain_db > target_3db_level && gain_db <= target_3db_level) ||
                (prev_gain_db >= target_3db_level && gain_db < target_3db_level)) {

                // 使用线性插值精确计算-3dB频率
                filter_cutoff_freq = InterpolateFrequency(prev_freq, prev_gain_db,
                                                         current_freq, gain_db,
                                                         target_3db_level);
            }
        }

        prev_freq = current_freq;
        prev_gain_db = gain_db;

        // 计算滚降率（在截止频率后的一个decade）
        if (filter_cutoff_freq > 0 && current_freq >= filter_cutoff_freq * 10.0f && filter_rolloff_rate == 0.0f) {
            float freq_ratio = current_freq / filter_cutoff_freq;
            float expected_2nd_order_gain = filter_passband_gain - 40.0f * log10f(freq_ratio);  // 理论-40dB/decade
            filter_rolloff_rate = (gain_db - filter_passband_gain) / log10f(freq_ratio);
        }

        // 输出频率和归一化电压幅度比
        printf("%.1f\t%.6f\r\n", current_freq, normalized_ratio);
    }

    // 只在缓冲区中保存最近的数据用于分析
    int buffer_idx = current_sweep_point % SWEEP_BUFFER_SIZE;
    sweep_results[buffer_idx].frequency = current_freq;
    sweep_results[buffer_idx].adc1_amplitude = adc1_rms;
    sweep_results[buffer_idx].adc2_amplitude = adc2_rms;
    sweep_results[buffer_idx].magnitude_db = magnitude_db;
    sweep_results[buffer_idx].phase_deg = phase_deg;

    total_sweep_points++;
}

void OutputSweepResults(void)
{
    // 扫频完成，无额外输出
}

void AnalyzeFilterCharacteristics(void)
{
    printf("=== FILTER ANALYSIS (Based on Recent Data) ===\r\n");

    // 分析缓冲区中的数据
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        printf("Insufficient data for analysis\r\n");
        printf("=== END OF ANALYSIS ===\r\n");
        return;
    }

    // 找到最大和最小幅度
    float max_magnitude = -100.0f;
    float min_magnitude = 100.0f;
    float max_freq = 0, min_freq = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
            if (sweep_results[i].magnitude_db < min_magnitude) {
                min_magnitude = sweep_results[i].magnitude_db;
                min_freq = sweep_results[i].frequency;
            }
        }
    }

    printf("Recent Data Analysis:\r\n");
    printf("Max Gain: %.2f dB at %.1f Hz\r\n", max_magnitude, max_freq);
    printf("Min Gain: %.2f dB at %.1f Hz\r\n", min_magnitude, min_freq);
    printf("Gain Range: %.2f dB\r\n", max_magnitude - min_magnitude);

    // 简单的滤波器类型判断
    float gain_variation = max_magnitude - min_magnitude;
    if (gain_variation > 10.0f) {
        printf("Filter Type: Significant frequency response variation detected\r\n");
    } else if (max_magnitude > -1.0f) {
        printf("Filter Type: Likely passband region\r\n");
    } else if (max_magnitude < -10.0f) {
        printf("Filter Type: Likely stopband region\r\n");
    } else {
        printf("Filter Type: Transition region\r\n");
    }

    printf("Note: Complete analysis requires full sweep data\r\n");
    printf("=== END OF ANALYSIS ===\r\n");
}

void AnalyzeLowPassFilter(void)
{
    printf("\r\n=== 2ND-ORDER RLC LOW-PASS FILTER ANALYSIS ===\r\n");

    // 基本参数输出
    printf("DC Gain: %.2f dB\r\n", filter_dc_gain);
    printf("Resonant Frequency: %.1f Hz\r\n", filter_resonant_freq);
    printf("Peak Gain: %.2f dB\r\n", filter_passband_gain);
    printf("Cutoff Frequency (-3dB): %.1f Hz\r\n", filter_cutoff_freq);

    // 优化的品质因数Q计算（多种方法综合）
    if (filter_resonant_freq > 0) {
        float q_methods[4] = {0};
        uint8_t valid_methods = 0;

        // 方法1：基于峰值增益（适用于Q > 0.707）
        float peak_above_dc = filter_passband_gain - filter_dc_gain;
        if (peak_above_dc > 0.5f && peak_above_dc < 40.0f) {  // 合理范围
            q_methods[valid_methods++] = powf(10.0f, peak_above_dc / 20.0f);
            printf("Q from peak gain: %.3f\r\n", q_methods[valid_methods-1]);
        }

        // 方法2：基于-3dB带宽（如果有截止频率）
        if (filter_cutoff_freq > filter_resonant_freq) {
            float bandwidth_3db = filter_cutoff_freq - filter_resonant_freq;
            if (bandwidth_3db > 0) {
                q_methods[valid_methods++] = filter_resonant_freq / bandwidth_3db;
                printf("Q from bandwidth: %.3f\r\n", q_methods[valid_methods-1]);
            }
        }

        // 方法3：基于谐振尖锐度（增益变化率）
        if (gain_derivative != 0.0f) {
            // 简化的尖锐度估算
            float sharpness = fabsf(gain_derivative) * filter_resonant_freq / 1000.0f;
            if (sharpness > 0.1f && sharpness < 10.0f) {
                q_methods[valid_methods++] = sharpness;
                printf("Q from sharpness: %.3f\r\n", q_methods[valid_methods-1]);
            }
        }

        // 综合多种方法计算最终Q值
        if (valid_methods > 0) {
            // 使用中位数减少异常值影响
            filter_q_factor = MedianFilter(q_methods, valid_methods);

            // 限制Q值在合理范围内
            if (filter_q_factor < 0.1f) filter_q_factor = 0.1f;
            if (filter_q_factor > 50.0f) filter_q_factor = 50.0f;
        }

        printf("Final Quality Factor Q: %.3f\r\n", filter_q_factor);

        // RLC滤波器阻尼类型判断
        if (filter_q_factor < 0.5f) {
            printf("Damping: Over-damped (Q < 0.5)\r\n");
            printf("Response: No peaking, monotonic rolloff\r\n");
        } else if (filter_q_factor < 0.707f) {
            printf("Damping: Under-damped but no peaking (0.5 < Q < 0.707)\r\n");
        } else if (filter_q_factor < 1.0f) {
            printf("Damping: Butterworth-like (Q = 0.707)\r\n");
            printf("Response: Maximally flat passband\r\n");
        } else if (filter_q_factor < 5.0f) {
            printf("Damping: Under-damped with peaking (Q > 0.707)\r\n");
            printf("Response: Resonant peak at %.1f Hz\r\n", filter_resonant_freq);
        } else {
            printf("Damping: High-Q resonant (Q > 5)\r\n");
            printf("Response: Sharp resonant peak\r\n");
        }
    } else {
        printf("Q Factor: Cannot calculate (insufficient data)\r\n");
    }

    // 滚降率分析
    if (filter_rolloff_rate != 0.0f) {
        printf("Measured Roll-off Rate: %.1f dB/decade\r\n", filter_rolloff_rate);
        if (filter_rolloff_rate > -30.0f && filter_rolloff_rate < -50.0f) {
            printf("Roll-off: Confirms 2nd-order filter (-40dB/decade theoretical)\r\n");
        }
    } else {
        printf("Roll-off Rate: -40 dB/decade (theoretical 2nd-order)\r\n");
    }

    // RLC滤波器特性
    printf("\r\nRLC Filter Characteristics:\r\n");
    printf("- Type: 2nd-order passive RLC low-pass\r\n");
    printf("- Order: 2 (40dB/decade rolloff)\r\n");

    if (filter_resonant_freq > 0) {
        printf("- Resonant frequency: %.1f Hz\r\n", filter_resonant_freq);

        if (filter_resonant_freq < 1000) {
            printf("- Category: Audio frequency range\r\n");
        } else if (filter_resonant_freq < 100000) {
            printf("- Category: RF/Communications range\r\n");
        } else {
            printf("- Category: High frequency range\r\n");
        }
    }

    // RLC设计参数估算
    printf("\r\nEstimated RLC Component Values:\r\n");
    if (filter_resonant_freq > 0 && filter_q_factor > 0) {
        float omega_r = 2.0f * 3.14159f * filter_resonant_freq;

        // 对于RLC低通滤波器：
        // fr = 1/(2π√LC)
        // Q = R√(C/L) 或 Q = (1/R)√(L/C)

        printf("For RLC low-pass filter design:\r\n");
        printf("- Resonant frequency: fr = 1/(2π√LC) = %.1f Hz\r\n", filter_resonant_freq);
        printf("- Quality factor: Q = R*sqrt(C/L) = %.3f\r\n", filter_q_factor);

        // 假设一些标准值进行计算
        float L_mH[] = {1.0f, 10.0f, 100.0f, 1000.0f};  // mH
        int num_L = sizeof(L_mH) / sizeof(L_mH[0]);

        printf("\nSuggested component combinations:\r\n");
        for (int i = 0; i < num_L; i++) {
            float L = L_mH[i] * 1e-3f;  // 转换为H
            float C = 1.0f / (omega_r * omega_r * L);  // 计算C
            float R = filter_q_factor * sqrtf(L / C);   // 计算R

            printf("  L=%.0fmH, C=%.1fnF, R=%.1fΩ\r\n",
                   L_mH[i], C * 1e9f, R);
        }
    }

    // 计算结果置信度
    float confidence = CalculateConfidenceLevel(filter_q_factor, filter_resonant_freq, filter_cutoff_freq);
    printf("\r\nResult Confidence Level: %.1f%%\r\n", confidence * 100.0f);

    if (confidence < 0.5f) {
        printf("WARNING: Low confidence results. Consider:\r\n");
        printf("- Checking signal connections\r\n");
        printf("- Improving signal quality\r\n");
        printf("- Using finer frequency steps\r\n");
    } else if (confidence > 0.8f) {
        printf("HIGH CONFIDENCE: Results are reliable\r\n");
    }

    printf("=== END OF RLC FILTER ANALYSIS ===\r\n\r\n");
}

void UpdateFilterDisplayOnLCD(void)
{
    // 清除之前的显示区域
    lcd_fill(10, 130, lcddev.width-10, 200, WHITE);

    // 显示滤波器参数
    char display_buffer[100];

    // 第一行：Q值和谐振频率
    if (filter_q_factor > 0 && filter_resonant_freq > 0) {
        sprintf(display_buffer, "Q = %.3f", filter_q_factor);
        lcd_show_string(10, 130, lcddev.width, 20, 16, display_buffer, RED);

        if (filter_resonant_freq >= 1000) {
            sprintf(display_buffer, "f = %.1f kHz", filter_resonant_freq/1000.0f);
        } else {
            sprintf(display_buffer, "f = %.1f Hz", filter_resonant_freq);
        }
        lcd_show_string(10, 150, lcddev.width, 20, 16, display_buffer, RED);
    }

    // 第二行：截止频率
    if (filter_cutoff_freq > 0) {
        if (filter_cutoff_freq >= 1000) {
            sprintf(display_buffer, "fc = %.1f kHz", filter_cutoff_freq/1000.0f);
        } else {
            sprintf(display_buffer, "fc = %.1f Hz", filter_cutoff_freq);
        }
        lcd_show_string(10, 170, lcddev.width, 20, 12, display_buffer, BLUE);
    }

    // 第三行：MAX262参数
    if (max262_f0_bits > 0) {
        sprintf(display_buffer, "MAX262: f0=0x%02X Q=0x%02X", max262_f0_bits, max262_q_bits);
        lcd_show_string(10, 190, lcddev.width, 20, 12, display_buffer, ORANGE);
    }
}

void CalculateMAX262Parameters(void)
{
    printf("\r\n=== MAX262 CONFIGURATION PARAMETERS ===\r\n");

    if (filter_cutoff_freq <= 0 || filter_q_factor <= 0) {
        printf("Error: Invalid filter parameters for MAX262 calculation\r\n");
        printf("=== END OF MAX262 PARAMETERS ===\r\n\r\n");
        return;
    }

    // MAX262的基本关系：
    // f0 = fCLK / (256 * N)，其中N由f0控制位决定
    // Q值由Q控制位决定

    printf("Measured Filter Parameters:\r\n");
    printf("- Cutoff Frequency: %.1f Hz\r\n", filter_cutoff_freq);
    printf("- Q Factor: %.3f\r\n", filter_q_factor);

    // 计算f0控制位
    // MAX262的f0对应谐振频率，不是截止频率
    float target_freq = filter_resonant_freq;

    // 尝试不同的时钟频率
    float clock_options[] = {1000000.0f, 2000000.0f, 4000000.0f, 8000000.0f};
    int num_clocks = sizeof(clock_options) / sizeof(clock_options[0]);

    printf("\r\nMAX262 Configuration Options:\r\n");

    for (int clk_idx = 0; clk_idx < num_clocks; clk_idx++) {
        float fclk = clock_options[clk_idx];

        // 计算N值：N = fCLK / (256 * f0)
        float N_exact = fclk / (256.0f * target_freq);

        // N的范围是1到256
        if (N_exact >= 1.0f && N_exact <= 256.0f) {
            uint8_t N = (uint8_t)(N_exact + 0.5f);  // 四舍五入
            float actual_f0 = fclk / (256.0f * N);
            float freq_error = ((actual_f0 - target_freq) / target_freq) * 100.0f;

            // f0控制位计算（8位）
            max262_f0_bits = N - 1;  // MAX262使用N-1作为控制字

            printf("\r\nClock: %.1f MHz\r\n", fclk / 1000000.0f);
            printf("  N = %d (0x%02X)\r\n", N, N);
            printf("  f0 control bits = 0x%02X\r\n", max262_f0_bits);
            printf("  Actual f0 = %.1f Hz\r\n", actual_f0);
            printf("  Frequency error = %.2f%%\r\n", freq_error);
        }
    }

    // Q值控制位计算
    // MAX262的Q值范围：0.5到256
    printf("\r\nQ Factor Configuration:\r\n");

    if (filter_q_factor >= 0.5f && filter_q_factor <= 256.0f) {
        // MAX262的Q控制位与Q值的关系（简化）
        // 实际关系比较复杂，这里给出近似计算

        if (filter_q_factor <= 1.0f) {
            max262_q_bits = (uint8_t)(filter_q_factor * 32.0f);
        } else if (filter_q_factor <= 4.0f) {
            max262_q_bits = (uint8_t)(32.0f + (filter_q_factor - 1.0f) * 64.0f);
        } else {
            max262_q_bits = (uint8_t)(224.0f + (filter_q_factor - 4.0f) * 8.0f);
        }

        if (max262_q_bits > 255) max262_q_bits = 255;

        printf("  Target Q = %.3f\r\n", filter_q_factor);
        printf("  Q control bits = 0x%02X (%d)\r\n", max262_q_bits, max262_q_bits);
    } else {
        printf("  Q value %.3f is out of MAX262 range (0.5 to 256)\r\n", filter_q_factor);
    }

    // 模式控制位（低通滤波器）
    max262_mode_bits = 0x00;  // 低通模式
    printf("\r\nFilter Mode Configuration:\r\n");
    printf("  Mode: Low-pass filter\r\n");
    printf("  Mode control bits = 0x%02X\r\n", max262_mode_bits);

    // 推荐的完整配置
    printf("\r\n=== RECOMMENDED MAX262 CONFIGURATION ===\r\n");
    printf("For %.1f MHz clock frequency:\r\n", max262_clock_freq / 1000000.0f);
    printf("  f0 register = 0x%02X\r\n", max262_f0_bits);
    printf("  Q register  = 0x%02X\r\n", max262_q_bits);
    printf("  Mode register = 0x%02X (Low-pass)\r\n", max262_mode_bits);

    // SPI写入命令示例
    printf("\r\nSPI Write Commands:\r\n");
    printf("  Write f0:   CS=0, SPI_Write(0x%02X), CS=1\r\n", max262_f0_bits);
    printf("  Write Q:    CS=0, SPI_Write(0x%02X), CS=1\r\n", max262_q_bits | 0x80);  // Q寄存器地址位
    printf("  Write Mode: CS=0, SPI_Write(0x%02X), CS=1\r\n", max262_mode_bits | 0x40); // Mode寄存器地址位

    printf("=== END OF MAX262 PARAMETERS ===\r\n\r\n");

    // 自动配置MAX262硬件
    if (filter_resonant_freq > 0 && filter_q_factor > 0) {
        printf("=== AUTO-CONFIGURING MAX262 HARDWARE ===\r\n");

        // 配置滤波器1为测得的参数
        printf("Configuring MAX262 Filter1...\r\n");
        MAX262_ConfigLowPass(1, filter_resonant_freq, filter_q_factor);

        printf("MAX262 Filter1 configured:\r\n");
        printf("- Mode: Low-pass\r\n");
        printf("- Frequency: %.1f Hz\r\n", filter_resonant_freq);
        printf("- Q Factor: %.3f\r\n", filter_q_factor);

        printf("=== MAX262 AUTO-CONFIGURATION COMPLETE ===\r\n\r\n");
    } else {
        printf("=== MAX262 AUTO-CONFIGURATION SKIPPED ===\r\n");
        printf("Reason: Invalid filter parameters\r\n\r\n");
    }
}

void AnalyzeBandwidthDetails(void)
{
    printf("\r\n=== BANDWIDTH ANALYSIS DETAILS ===\r\n");

    printf("Current -3dB Detection Method:\r\n");
    printf("1. Reference Level: ");
    if (filter_dc_gain != 0.0f) {
        printf("DC Gain = %.2f dB\r\n", filter_dc_gain);
    } else {
        printf("Peak Gain = %.2f dB\r\n", filter_passband_gain);
    }

    float reference_gain = (filter_dc_gain != 0.0f) ? filter_dc_gain : filter_passband_gain;
    printf("2. Target -3dB Level = %.2f dB\r\n", reference_gain - 3.0f);
    printf("3. Detected -3dB Frequency = %.1f Hz\r\n", filter_cutoff_freq);

    // 计算实际的3dB带宽
    if (filter_cutoff_freq > 0) {
        printf("4. 3dB Bandwidth = 0 to %.1f Hz\r\n", filter_cutoff_freq);
        printf("5. Bandwidth = %.1f Hz\r\n", filter_cutoff_freq);

        // 如果有谐振频率，计算相对带宽
        if (filter_resonant_freq > 0) {
            float relative_bw = filter_cutoff_freq / filter_resonant_freq;
            printf("6. Relative Bandwidth = fc/fr = %.3f\r\n", relative_bw);
        }
    }

    // Q值计算方法说明
    printf("\r\nQ Factor Calculation Methods:\r\n");
    if (filter_resonant_freq > 0 && filter_cutoff_freq > 0) {
        // 方法1：传统定义
        float q_traditional = filter_resonant_freq / filter_cutoff_freq;
        printf("Method 1 (fr/fc): Q = %.3f\r\n", q_traditional);

        // 方法2：基于峰值增益
        if (filter_passband_gain > filter_dc_gain) {
            float peak_above_dc = filter_passband_gain - filter_dc_gain;
            float q_from_peak = powf(10.0f, peak_above_dc / 20.0f);
            printf("Method 2 (from peak): Q = %.3f\r\n", q_from_peak);
        }

        // 方法3：基于带宽
        if (filter_cutoff_freq > filter_resonant_freq) {
            float bandwidth = filter_cutoff_freq - filter_resonant_freq;
            float q_from_bw = filter_resonant_freq / bandwidth;
            printf("Method 3 (fr/BW): Q = %.3f\r\n", q_from_bw);
        }
    }

    // 检测方法的局限性说明
    printf("\r\nDetection Method Limitations:\r\n");
    printf("- Single-pass detection (may miss optimal point)\r\n");
    printf("- Frequency step size: 200 Hz\r\n");
    printf("- Detection accuracy: ±100 Hz\r\n");

    if (filter_cutoff_freq == 0) {
        printf("- WARNING: No -3dB point detected!\r\n");
        printf("- Possible reasons:\r\n");
        printf("  * Filter cutoff > 400kHz (beyond sweep range)\r\n");
        printf("  * Filter has no clear -3dB point\r\n");
        printf("  * Detection threshold too strict\r\n");
    }

    printf("=== END OF BANDWIDTH ANALYSIS ===\r\n\r\n");
}

//-----------------------------------------------------------------
// 优化的ADC数据处理函数
//-----------------------------------------------------------------

float ProcessADCData_Enhanced(uint16_t* buffer, uint16_t size)
{
    if (size == 0) return 0.0f;

    // 1. 异常值检测和移除
    float temp_buffer[1024];
    uint16_t valid_count = 0;

    // 计算初步统计量
    uint32_t sum = 0;
    for (int i = 0; i < size; i++) {
        sum += buffer[i];
    }
    float mean = (float)sum / size;

    // 计算标准差
    float variance = 0.0f;
    for (int i = 0; i < size; i++) {
        float diff = buffer[i] - mean;
        variance += diff * diff;
    }
    float std_dev = sqrtf(variance / size);

    // 移除3σ以外的异常值
    for (int i = 0; i < size; i++) {
        float diff = fabsf(buffer[i] - mean);
        if (diff <= 3.0f * std_dev) {
            temp_buffer[valid_count++] = (float)buffer[i];
        }
    }

    if (valid_count == 0) return mean;

    // 2. 重新计算直流分量（使用中位数更稳定）
    float dc_value = MedianFilter(temp_buffer, valid_count);

    return dc_value;
}

float CalculateRMS_Enhanced(uint16_t* buffer, uint16_t size, float dc_value)
{
    if (size == 0) return 0.0f;

    // 1. 去除直流分量并计算RMS
    float ac_sum = 0.0f;
    uint16_t valid_count = 0;

    for (int i = 0; i < size; i++) {
        float ac_component = buffer[i] - dc_value;

        // 简单的异常值过滤
        if (fabsf(ac_component) < 2048.0f) {  // 避免极端值
            ac_sum += ac_component * ac_component;
            valid_count++;
        }
    }

    if (valid_count == 0) return 0.0f;

    float rms = sqrtf(ac_sum / valid_count);

    // 2. 应用低通滤波平滑RMS值
    static float rms_history[5] = {0};
    static uint8_t rms_index = 0;

    rms_history[rms_index] = rms;
    rms_index = (rms_index + 1) % 5;

    // 计算移动平均
    float rms_avg = 0.0f;
    for (int i = 0; i < 5; i++) {
        rms_avg += rms_history[i];
    }
    rms_avg /= 5.0f;

    return rms_avg;
}

float MedianFilter(float* data, uint16_t size)
{
    if (size == 0) return 0.0f;
    if (size == 1) return data[0];

    // 简单的冒泡排序（对于小数组足够）
    float sorted[1024];
    for (int i = 0; i < size; i++) {
        sorted[i] = data[i];
    }

    for (int i = 0; i < size - 1; i++) {
        for (int j = 0; j < size - 1 - i; j++) {
            if (sorted[j] > sorted[j + 1]) {
                float temp = sorted[j];
                sorted[j] = sorted[j + 1];
                sorted[j + 1] = temp;
            }
        }
    }

    // 返回中位数
    if (size % 2 == 1) {
        return sorted[size / 2];
    } else {
        return (sorted[size / 2 - 1] + sorted[size / 2]) / 2.0f;
    }
}

float InterpolateFrequency(float freq1, float gain1, float freq2, float gain2, float target_gain)
{
    if (fabsf(gain2 - gain1) < 0.001f) {
        return (freq1 + freq2) / 2.0f;  // 避免除零
    }

    // 线性插值
    float ratio = (target_gain - gain1) / (gain2 - gain1);
    return freq1 + ratio * (freq2 - freq1);
}

void SmoothingFilter(float* data, uint16_t size, uint8_t window_size)
{
    if (size <= window_size || window_size < 3) return;

    float temp[1024];
    for (int i = 0; i < size; i++) {
        temp[i] = data[i];
    }

    // 移动平均滤波
    for (int i = window_size / 2; i < size - window_size / 2; i++) {
        float sum = 0.0f;
        for (int j = -window_size / 2; j <= window_size / 2; j++) {
            sum += temp[i + j];
        }
        data[i] = sum / window_size;
    }
}

//-----------------------------------------------------------------
// 数据质量评估和自适应处理函数
//-----------------------------------------------------------------

float EvaluateDataQuality(uint16_t* buffer, uint16_t size)
{
    if (size == 0) return 0.0f;

    // 计算信噪比估算
    float mean = 0.0f;
    for (int i = 0; i < size; i++) {
        mean += buffer[i];
    }
    mean /= size;

    // 计算信号变化量（标准差）
    float variance = 0.0f;
    for (int i = 0; i < size; i++) {
        float diff = buffer[i] - mean;
        variance += diff * diff;
    }
    float std_dev = sqrtf(variance / size);

    // 计算动态范围
    uint16_t min_val = 4095, max_val = 0;
    for (int i = 0; i < size; i++) {
        if (buffer[i] < min_val) min_val = buffer[i];
        if (buffer[i] > max_val) max_val = buffer[i];
    }
    float dynamic_range = max_val - min_val;

    // 质量评分 (0-1)
    float snr_score = (std_dev > 1.0f) ? 1.0f : std_dev;
    float range_score = (dynamic_range > 100.0f) ? 1.0f : dynamic_range / 100.0f;

    return (snr_score + range_score) / 2.0f;
}

void AdaptiveSampling(float data_quality)
{
    // 根据数据质量调整采样参数
    static uint8_t quality_counter = 0;

    if (data_quality < 0.3f) {
        quality_counter++;
        if (quality_counter > 3) {
            // 数据质量差，建议增加采样时间或降低频率步进
            printf("Warning: Poor data quality detected (%.2f)\r\n", data_quality);
            printf("Suggestion: Check signal connections or reduce frequency step\r\n");
            quality_counter = 0;
        }
    } else {
        quality_counter = 0;
    }
}

float CalculateConfidenceLevel(float q_factor, float resonant_freq, float cutoff_freq)
{
    float confidence = 1.0f;

    // Q值合理性检查
    if (q_factor < 0.1f || q_factor > 20.0f) {
        confidence *= 0.5f;
    }

    // 频率关系合理性检查
    if (resonant_freq > 0 && cutoff_freq > 0) {
        float freq_ratio = cutoff_freq / resonant_freq;
        if (freq_ratio < 0.5f || freq_ratio > 5.0f) {
            confidence *= 0.7f;
        }
    }

    // 频率范围检查
    if (resonant_freq < 100.0f || resonant_freq > 500000.0f) {
        confidence *= 0.6f;
    }

    return confidence;
}