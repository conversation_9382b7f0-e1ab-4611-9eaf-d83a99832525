<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_opt.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>STM32_262</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>8000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\List\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <Books>
        <Book>
          <Number>0</Number>
          <Title>Reference Manual</Title>
          <Path>DATASHTS\ST\STM32F10xxx.PDF</Path>
        </Book>
        <Book>
          <Number>1</Number>
          <Title>Technical Reference Manual</Title>
          <Path>datashts\arm\cortex_m3\r1p1\DDI0337E_CORTEX_M3_R1P1_TRM.PDF</Path>
        </Book>
        <Book>
          <Number>2</Number>
          <Title>Generic User Guide</Title>
          <Path>datashts\arm\cortex_m3\r2p1\DUI0552A_CORTEX_M3_DGUG.PDF</Path>
        </Book>
      </Books>
      <DllOpt>
        <SimDllName>SARMCM3.DLL</SimDllName>
        <SimDllArguments>-REMAP</SimDllArguments>
        <SimDlgDllName>DARMSTM.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pSTM32F103VC</SimDlgDllArguments>
        <TargetDllName>SARMCM3.DLL</TargetDllName>
        <TargetDllArguments></TargetDllArguments>
        <TargetDlgDllName>TARMSTM.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pSTM32F103VC</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>13</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>STLink\ST-LINKIII-KEIL_SWO.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(100=-1,-1,-1,-1,0)(110=-1,-1,-1,-1,0)(111=-1,-1,-1,-1,0)(1011=-1,-1,-1,-1,0)(180=-1,-1,-1,-1,0)(120=-1,-1,-1,-1,0)(121=955,0,1366,395,1)(122=-1,-1,-1,-1,0)(123=-1,-1,-1,-1,0)(124=955,373,1366,768,1)(125=-1,-1,-1,-1,0)(126=-1,-1,-1,-1,0)(140=-1,-1,-1,-1,0)(240=-1,-1,-1,-1,0)(190=-1,-1,-1,-1,0)(200=-1,-1,-1,-1,0)(170=-1,-1,-1,-1,0)(130=-1,-1,-1,-1,0)(131=-1,-1,-1,-1,0)(132=-1,-1,-1,-1,0)(133=-1,-1,-1,-1,0)(160=-1,-1,-1,-1,0)(161=-1,-1,-1,-1,0)(162=-1,-1,-1,-1,0)(210=-1,-1,-1,-1,0)(211=-1,-1,-1,-1,0)(220=-1,-1,-1,-1,0)(221=-1,-1,-1,-1,0)(230=-1,-1,-1,-1,0)(231=-1,-1,-1,-1,0)(232=-1,-1,-1,-1,0)(233=-1,-1,-1,-1,0)(150=-1,-1,-1,-1,0)(151=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name>(105=-1,-1,-1,-1,0)(106=-1,-1,-1,-1,0)(107=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ST-LINKIII-KEIL_SWO</Key>
          <Name>-U-O206 -O206 -S3 -C0 -N00("ARM CoreSight SW-DP") -D00(1BA01477) -L00(0) -TO18 -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO15 -********** -FC800 -FN1 -FF0STM32F10x_512 -********** -FL080000</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>-O14 -S0 -C0 -N00("ARM Cortex-M3") -D00(1BA00477) -L00(4) -FO7 -********** -FC800 -FN1 -FF0STM32F10x_512 -********** -FL040000)</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint>
        <Bp>
          <Number>0</Number>
          <Type>0</Type>
          <LineNumber>39</LineNumber>
          <EnabledFlag>1</EnabledFlag>
          <Address>134218192</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>G:\脛拢驴茅\脠铆录镁\32\STM32潞脣脨脛掳氓\脩鹿驴脴脗脣虏篓脝梅\MAX262\max262\STM32f103_262 - 赂卤卤戮\User\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\\STM32_262\../User/main.c\39</Expression>
        </Bp>
      </Breakpoint>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>sf</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>sq,0x10</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>way</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>a</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>1</WinNumber>
          <ItemText>n</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>1</WinNumber>
          <ItemText>i</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>1</WinNumber>
          <ItemText>sq</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>1</WinNumber>
          <ItemText>temp</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>1</WinNumber>
          <ItemText>sf</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>1</WinNumber>
          <ItemText>temp</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>1</WinNumber>
          <ItemText>temp</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>1</WinNumber>
          <ItemText>sq</ItemText>
        </Ww>
      </WatchWindow1>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>User</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>22</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>1</TopLine>
      <CurrentLine>10</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\User\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\User\Delay.c</PathWithFileName>
      <FilenameWithoutPath>Delay.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\User\PeripheralInit.c</PathWithFileName>
      <FilenameWithoutPath>PeripheralInit.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>1</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>15</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>5</TopLine>
      <CurrentLine>20</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\User\MAX262.c</PathWithFileName>
      <FilenameWithoutPath>MAX262.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>StdPeriph_Dreiver</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\STM32F10x_StdPeriph_Driver\src\misc.c</PathWithFileName>
      <FilenameWithoutPath>misc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_rcc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>CMSIS</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CoreSupport\core_cm3.c</PathWithFileName>
      <FilenameWithoutPath>core_cm3.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c</PathWithFileName>
      <FilenameWithoutPath>system_stm32f10x.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Startup</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>39260940</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>5</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s</PathWithFileName>
      <FilenameWithoutPath>startup_stm32f10x_hd.s</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
